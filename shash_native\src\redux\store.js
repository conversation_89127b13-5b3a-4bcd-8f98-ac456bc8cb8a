import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import authReducer from './authSlice';
import dashboardReducer from './dashboardSlice';
import chatBotReducer from './chatBotSlice';
import ticketReducer from './ticketSlice';
import packageReducer from './packageSlice';
import studentReducer from './studentSlice';
import bannerReducer from './bannerSlice';
import reportErrorReducer, { reportError } from './reportErrorSlice';
import popupReducer from './popupSlice';
import cartReducer from './cartSlice';
import subscriptionReducer from './subscriptionSlice';
import contactReducer from './contactSlice';

// Variables for error tracking
let previousErrors = {};
let errorTimeout = null;

// Error tracking middleware with optimization
const errorTrackingMiddleware = (storeAPI) => (next) => (action) => {
  const result = next(action); // Process the action first
  const state = storeAPI.getState(); // Get the updated state

  const errorData = extractErrors(state); // Extract errors

  if (Object.keys(errorData).length > 0 && hasNewErrors(errorData)) {
    // Clear previous timeout (if any)
    if (errorTimeout) {
      clearTimeout(errorTimeout);
    }    // Delay dispatching the error report by 2 seconds to prevent rapid calls
    errorTimeout = setTimeout(() => {
      storeAPI.dispatch(reportError({ 
        error_data: {
          ...errorData,
          app_type: 'React Native Android'
        } 
      }));
      previousErrors = errorData; // Update previous errors
    }, 2000);
  }

  return result;
};

// Function to extract errors from the store
const extractErrors = (state) => {
  let errors = {};

  for (const key in state) {
    if (state[key]?.error) {
      errors[key] = { error: state[key].error };
    }
  }

  return errors;
};

// Function to check if there are new errors not reported before
const hasNewErrors = (currentErrors) => {
  return JSON.stringify(previousErrors) !== JSON.stringify(currentErrors);
};

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['auth', 'dashboard', 'chatBot', 'ticket', 'packages', 'student', 'popup', 'cart', 'subscription', 'contacts'], // Added contacts to persist state
};

const rootReducer = combineReducers({
  auth: authReducer,
  dashboard: dashboardReducer,
  chatBot: chatBotReducer,
  ticket: ticketReducer,
  packages: packageReducer,
  student: studentReducer,
  banner: bannerReducer,
  errorReport: reportErrorReducer,
  popup: popupReducer,
  cart: cartReducer,
  subscription: subscriptionReducer,
  contacts: contactReducer, // Added contacts reducer
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(errorTrackingMiddleware),
});

export const persistor = persistStore(store);
